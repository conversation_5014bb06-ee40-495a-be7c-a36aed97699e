import { getCloudflareContext } from '@opennextjs/cloudflare'
import { NextRequest, NextResponse } from 'next/server'

// Define the Cloudflare environment interface
interface CloudflareEnv {
  bucket: R2Bucket
}

// Extend the global process.env to include Cloudflare bindings
declare global {
  namespace NodeJS {
    interface ProcessEnv extends CloudflareEnv {}
  }
}

export async function POST(request: NextRequest) {
  // Define Cloudflare environment variables
  const { env } = await getCloudflareContext({ async: true })
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'Invalid file type' }, { status: 400 })
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File too large' }, { status: 400 })
    }

    // Get R2 bucket from Cloudflare environment
    // In Cloudflare Workers, the bucket is available as a global binding
    const bucket = env.bucket

    if (!bucket) {
      return NextResponse.json({ error: 'R2 bucket not configured' }, { status: 500 })
    }

    // Generate unique filename
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const extension = file.name.split('.').pop()
    const filename = `${timestamp}-${randomString}.${extension}`

    // Upload to R2
    const arrayBuffer = await file.arrayBuffer()
    await bucket.put(filename, arrayBuffer, {
      httpMetadata: {
        contentType: file.type,
      },
    })

    // Return the URL - you'll need to replace this with your actual R2 public URL
    // For now, we'll use a placeholder that you can configure later
    const imageUrl = `https://static.clinecoder.com/${filename}`

    return NextResponse.json({
      success: true,
      url: imageUrl,
      filename
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json({ error: 'Upload failed' }, { status: 500 })
  }
}
