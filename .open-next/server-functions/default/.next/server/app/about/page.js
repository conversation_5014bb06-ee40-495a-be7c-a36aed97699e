(()=>{var e={};e.id=220,e.ids=[220],e.modules={695:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(1109).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/projects/clinecoder.com/app/about/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/projects/clinecoder.com/app/about/page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1053:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(5921),n=r(4100),i=r(8234);function o(){return(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsx)("header",{className:"container mx-auto py-6",children:(0,s.jsx)(n.A,{})}),(0,s.jsx)("main",{className:"container mx-auto px-4 py-12",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-8",children:"About NeuralPulse"}),(0,s.jsxs)("div",{className:"prose prose-invert prose-purple max-w-none",children:[(0,s.jsx)("p",{className:"text-xl text-gray-300 mb-8",children:"NeuralPulse is dedicated to exploring the frontiers of artificial intelligence, with a focus on recent advancements in AI, GenAI, Computer Vision, and Deep Learning."}),(0,s.jsx)("h2",{children:"Our Mission"}),(0,s.jsx)("p",{children:"Our mission is to provide insightful, accessible, and cutting-edge content about the rapidly evolving field of artificial intelligence. We aim to bridge the gap between technical research and practical applications, making complex AI concepts understandable to a wider audience."}),(0,s.jsx)("h2",{children:"What We Cover"}),(0,s.jsx)("p",{children:"At NeuralPulse, we focus on several key areas in the AI landscape:"}),(0,s.jsxs)("ul",{children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Generative AI"}),": From GANs to diffusion models, we explore how AI is creating increasingly realistic content across various media."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Computer Vision"}),": We delve into how machines perceive and understand visual information, and how this technology is transforming industries."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Deep Learning"}),": We examine the architectures, techniques, and applications that are pushing the boundaries of what AI can achieve."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"AI Ethics"}),": We discuss the ethical implications of AI development and deployment, advocating for responsible innovation."]})]}),(0,s.jsx)("h2",{children:"Our Team"}),(0,s.jsx)("p",{children:"NeuralPulse is run by a team of AI researchers, engineers, and enthusiasts who are passionate about the potential of artificial intelligence to transform our world for the better."}),(0,s.jsx)("h2",{children:"Contact Us"}),(0,s.jsxs)("p",{children:["Have a question, suggestion, or want to collaborate? We'd love to hear from you! Reach out to us at"," ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-400 hover:text-purple-300",children:"<EMAIL>"}),"."]})]})]})}),(0,s.jsx)("footer",{className:"border-t border-gray-800 py-12",children:(0,s.jsx)(i.A,{})})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5322:(e,t,r)=>{Promise.resolve().then(r.bind(r,1053))},5814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(8773),n=r(8018),i=r(4828),o=r.n(i),a=r(6395),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,695)),"/home/<USER>/projects/clinecoder.com/app/about/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,7765))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1638)),"/home/<USER>/projects/clinecoder.com/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,2264,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,1121,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5678,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,7765))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/projects/clinecoder.com/app/about/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7170:(e,t,r)=>{Promise.resolve().then(r.bind(r,695))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[283,739,837,506,622],()=>r(5814));module.exports=s})();