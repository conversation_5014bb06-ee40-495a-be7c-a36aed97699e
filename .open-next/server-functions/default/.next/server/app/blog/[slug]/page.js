(()=>{var e={};e.id=953,e.ids=[953],e.modules={788:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:a,blurHeight:i,blurDataURL:n,objectFit:o}=e,s=a?40*a:t,l=i?40*i:r,d=s&&l?"viewBox='0 0 "+s+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},900:(e,t,r)=>{Promise.resolve().then(r.bind(r,9273))},1638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>s});var a=r(6147);r(9110);var i=r(3432),n=r.n(i),o=r(2579);let s={title:"Cline coder",description:"Exploring the frontiers of artificial intelligence, generative AI, computer vision, and deep learning."};function l({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsxs)("body",{className:n().className,children:[e,(0,a.jsx)(o.Toaster,{})]})})}},1672:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return a}});let r=["default","imgix","cloudinary","akamai","custom"],a={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},1674:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(8773),i=r(8018),n=r(4828),o=r.n(n),s=r(6395),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let d={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9882)),"/home/<USER>/projects/clinecoder.com/app/blog/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,7765))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1638)),"/home/<USER>/projects/clinecoder.com/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,2264,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,1121,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5678,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,7765))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/projects/clinecoder.com/app/blog/[slug]/page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2579:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>a});let a=(0,r(1109).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/projects/clinecoder.com/components/ui/sonner.tsx","Toaster")},2634:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4126,23)),Promise.resolve().then(r.t.bind(r,7432,23)),Promise.resolve().then(r.t.bind(r,4828,23)),Promise.resolve().then(r.t.bind(r,1215,23)),Promise.resolve().then(r.t.bind(r,2375,23)),Promise.resolve().then(r.t.bind(r,2271,23)),Promise.resolve().then(r.t.bind(r,3327,23)),Promise.resolve().then(r.t.bind(r,1801,23))},2673:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:a,width:i,quality:n}=e,o=n||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(a)+"&w="+i+"&q="+o+(a.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),r.__next_img_default=!0;let a=r},2802:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,8016,23)),Promise.resolve().then(r.t.bind(r,1918,23)),Promise.resolve().then(r.t.bind(r,8654,23)),Promise.resolve().then(r.t.bind(r,8305,23)),Promise.resolve().then(r.t.bind(r,6405,23)),Promise.resolve().then(r.t.bind(r,7709,23)),Promise.resolve().then(r.t.bind(r,3813,23)),Promise.resolve().then(r.t.bind(r,9407,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3034:(e,t,r)=>{let{createProxy:a}=r(5010);e.exports=a("/home/<USER>/projects/clinecoder.com/node_modules/.pnpm/next@15.2.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/app-dir/link.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5856:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},7113:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return s}});let a=r(1011),i=r(7813),n=r(9417),o=a._(r(2673));function s(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=n.Image},7627:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3034,23)),Promise.resolve().then(r.t.bind(r,9417,23))},7715:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,1564,23)),Promise.resolve().then(r.t.bind(r,1047,23))},7765:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(9864);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},7813:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(5856);let a=r(788),i=r(1672);function n(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r,s;let l,d,c,{src:h,sizes:p,unoptimized:u=!1,priority:m=!1,loading:g,className:f,quality:b,width:v,height:y,fill:x=!1,style:w,overrideSrc:k,onLoad:A,onLoadingComplete:j,placeholder:N="empty",blurDataURL:I,fetchPriority:P,decoding:z="async",layout:T,objectFit:C,objectPosition:E,lazyBoundary:M,lazyRoot:G,...S}=e,{imgConf:_,showAltText:R,blurComplete:q,defaultLoader:L}=t,O=_||i.imageConfigDefault;if("allSizes"in O)l=O;else{let e=[...O.deviceSizes,...O.imageSizes].sort((e,t)=>e-t),t=O.deviceSizes.sort((e,t)=>e-t),a=null==(r=O.qualities)?void 0:r.sort((e,t)=>e-t);l={...O,allSizes:e,deviceSizes:t,qualities:a}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let D=S.loader||L;delete S.loader,delete S.srcSet;let F="__next_img_default"in D;if(F){if("custom"===l.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=D;D=t=>{let{config:r,...a}=t;return e(a)}}if(T){"fill"===T&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[T];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[T];t&&!p&&(p=t)}let B="",W=o(v),V=o(y);if((s=h)&&"object"==typeof s&&(n(s)||void 0!==s.src)){let e=n(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,I=I||e.blurDataURL,B=e.src,!x){if(W||V){if(W&&!V){let t=W/e.width;V=Math.round(e.height*t)}else if(!W&&V){let t=V/e.height;W=Math.round(e.width*t)}}else W=e.width,V=e.height}}let $=!m&&("lazy"===g||void 0===g);(!(h="string"==typeof h?h:B)||h.startsWith("data:")||h.startsWith("blob:"))&&(u=!0,$=!1),l.unoptimized&&(u=!0),F&&!l.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(u=!0);let H=o(b),U=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:C,objectPosition:E}:{},R?{}:{color:"transparent"},w),J=q||"empty"===N?null:"blur"===N?'url("data:image/svg+xml;charset=utf-8,'+(0,a.getImageBlurSvg)({widthInt:W,heightInt:V,blurWidth:d,blurHeight:c,blurDataURL:I||"",objectFit:U.objectFit})+'")':'url("'+N+'")',K=J?{backgroundSize:U.objectFit||"cover",backgroundPosition:U.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},X=function(e){let{config:t,src:r,unoptimized:a,width:i,quality:n,sizes:o,loader:s}=e;if(a)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:a,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let a;a=e.exec(r);a)t.push(parseInt(a[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=a[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:a,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,o),c=l.length-1;return{sizes:o||"w"!==d?o:"100vw",srcSet:l.map((e,a)=>s({config:t,src:r,quality:n,width:e})+" "+("w"===d?e:a+1)+d).join(", "),src:s({config:t,src:r,quality:n,width:l[c]})}}({config:l,src:h,unoptimized:u,width:W,quality:H,sizes:p,loader:D});return{props:{...S,loading:$?"lazy":g,fetchPriority:P,width:W,height:V,decoding:z,className:f,style:{...U,...K},sizes:X.sizes,srcSet:X.srcSet,src:k||X.src},meta:{unoptimized:u,priority:m,placeholder:N,fill:x}}}},8164:(e,t,r)=>{Promise.resolve().then(r.bind(r,2579))},9110:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9273:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});var a=r(5921),i=r(4750),n=r(6737);let o=({...e})=>{let{theme:t="system"}=(0,i.D)();return(0,a.jsx)(n.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9417:(e,t,r)=>{let{createProxy:a}=r(5010);e.exports=a("/home/<USER>/projects/clinecoder.com/node_modules/.pnpm/next@15.2.3_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/image-component.js")},9551:e=>{"use strict";e.exports=require("url")},9882:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eF});var a=r(6147),i=r(3034),n=r.n(i),o=r(7113),s=r.n(o),l=r(1478);let d=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let p=(0,l.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:o,...s},d)=>(0,l.createElement)("svg",{ref:d,...h,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:c("lucide",i),...s},[...o.map(([e,t])=>(0,l.createElement)(e,t)),...Array.isArray(n)?n:[n]])),u=(e,t)=>{let r=(0,l.forwardRef)(({className:r,...a},i)=>(0,l.createElement)(p,{ref:i,iconNode:t,className:c(`lucide-${d(e)}`,r),...a}));return r.displayName=`${e}`,r},m=u("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),g=u("BrainCircuit",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M9 13a4.5 4.5 0 0 0 3-4",key:"10igwf"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M16 8V5a2 2 0 0 1 2-2",key:"u6izg6"}],["circle",{cx:"16",cy:"13",r:".5",key:"ry7gng"}],["circle",{cx:"18",cy:"3",r:".5",key:"1aiba7"}],["circle",{cx:"20",cy:"21",r:".5",key:"yhc1fs"}],["circle",{cx:"20",cy:"8",r:".5",key:"1e43v0"}]]),f=u("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),b=u("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),v=u("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),y=u("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),x=u("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);function w(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var k=l.forwardRef((e,t)=>{let{children:r,...i}=e,n=l.Children.toArray(r),o=n.find(N);if(o){let e=o.props.children,r=n.map(t=>t!==o?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,a.jsx)(A,{...i,ref:t,children:l.isValidElement(e)?l.cloneElement(e,void 0,r):null})}return(0,a.jsx)(A,{...i,ref:t,children:r})});k.displayName="Slot";var A=l.forwardRef((e,t)=>{let{children:r,...a}=e;if(l.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let a in t){let i=e[a],n=t[a];/^on[A-Z]/.test(a)?i&&n?r[a]=(...e)=>{n(...e),i(...e)}:i&&(r[a]=i):"style"===a?r[a]={...i,...n}:"className"===a&&(r[a]=[i,n].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==l.Fragment&&(i.ref=t?function(...e){return t=>{let r=!1,a=e.map(e=>{let a=w(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():w(e[t],null)}}}}(t,e):e),l.cloneElement(r,i)}return l.Children.count(r)>1?l.Children.only(null):null});A.displayName="SlotClone";var j=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function N(e){return l.isValidElement(e)&&e.type===j}function I(){for(var e,t,r=0,a="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,a,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t)){var n=t.length;for(r=0;r<n;r++)t[r]&&(a=e(t[r]))&&(i&&(i+=" "),i+=a)}else for(a in t)t[a]&&(i&&(i+=" "),i+=a)}return i}(e))&&(a&&(a+=" "),a+=t);return a}let P=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,z=e=>{let t=M(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),T(r,t)||E(e)},getConflictingClassGroupIds:(e,t)=>{let i=r[e]||[];return t&&a[e]?[...i,...a[e]]:i}}},T=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],a=t.nextPart.get(r),i=a?T(e.slice(1),a):void 0;if(i)return i;if(0===t.validators.length)return;let n=e.join("-");return t.validators.find(({validator:e})=>e(n))?.classGroupId},C=/^\[(.+)\]$/,E=e=>{if(C.test(e)){let t=C.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},M=e=>{let{theme:t,classGroups:r}=e,a={nextPart:new Map,validators:[]};for(let e in r)G(r[e],a,e,t);return a},G=(e,t,r,a)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:S(t,e)).classGroupId=r;return}if("function"==typeof e){if(_(e)){G(e(a),t,r,a);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,i])=>{G(i,S(t,e),r,a)})})},S=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},_=e=>e.isThemeGetter,R=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,a=new Map,i=(i,n)=>{r.set(i,n),++t>e&&(t=0,a=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=a.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}},q=e=>{let{prefix:t,experimentalParseClassName:r}=e,a=e=>{let t;let r=[],a=0,i=0,n=0;for(let o=0;o<e.length;o++){let s=e[o];if(0===a&&0===i){if(":"===s){r.push(e.slice(n,o)),n=o+1;continue}if("/"===s){t=o;continue}}"["===s?a++:"]"===s?a--:"("===s?i++:")"===s&&i--}let o=0===r.length?e:e.substring(n),s=L(o);return{modifiers:r,hasImportantModifier:s!==o,baseClassName:s,maybePostfixModifierPosition:t&&t>n?t-n:void 0}};if(t){let e=t+":",r=a;a=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=a;a=t=>r({className:t,parseClassName:e})}return a},L=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,O=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],a=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...a.sort(),e),a=[]):a.push(e)}),r.push(...a.sort()),r}},D=e=>({cache:R(e.cacheSize),parseClassName:q(e),sortModifiers:O(e),...z(e)}),F=/\s+/,B=(e,t)=>{let{parseClassName:r,getClassGroupId:a,getConflictingClassGroupIds:i,sortModifiers:n}=t,o=[],s=e.trim().split(F),l="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:d,modifiers:c,hasImportantModifier:h,baseClassName:p,maybePostfixModifierPosition:u}=r(t);if(d){l=t+(l.length>0?" "+l:l);continue}let m=!!u,g=a(m?p.substring(0,u):p);if(!g){if(!m||!(g=a(p))){l=t+(l.length>0?" "+l:l);continue}m=!1}let f=n(c).join(":"),b=h?f+"!":f,v=b+g;if(o.includes(v))continue;o.push(v);let y=i(g,m);for(let e=0;e<y.length;++e){let t=y[e];o.push(b+t)}l=t+(l.length>0?" "+l:l)}return l};function W(){let e,t,r=0,a="";for(;r<arguments.length;)(e=arguments[r++])&&(t=V(e))&&(a&&(a+=" "),a+=t);return a}let V=e=>{let t;if("string"==typeof e)return e;let r="";for(let a=0;a<e.length;a++)e[a]&&(t=V(e[a]))&&(r&&(r+=" "),r+=t);return r},$=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},H=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,U=/^\((?:(\w[\w-]*):)?(.+)\)$/i,J=/^\d+\/\d+$/,K=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,X=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Z=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Q=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ee=e=>J.test(e),et=e=>!!e&&!Number.isNaN(Number(e)),er=e=>!!e&&Number.isInteger(Number(e)),ea=e=>e.endsWith("%")&&et(e.slice(0,-1)),ei=e=>K.test(e),en=()=>!0,eo=e=>X.test(e)&&!Z.test(e),es=()=>!1,el=e=>Y.test(e),ed=e=>Q.test(e),ec=e=>!ep(e)&&!ev(e),eh=e=>eN(e,eE,es),ep=e=>H.test(e),eu=e=>eN(e,eM,eo),em=e=>eN(e,eG,et),eg=e=>eN(e,eP,es),ef=e=>eN(e,eT,ed),eb=e=>eN(e,es,el),ev=e=>U.test(e),ey=e=>eI(e,eM),ex=e=>eI(e,eS),ew=e=>eI(e,eP),ek=e=>eI(e,eE),eA=e=>eI(e,eT),ej=e=>eI(e,e_,!0),eN=(e,t,r)=>{let a=H.exec(e);return!!a&&(a[1]?t(a[1]):r(a[2]))},eI=(e,t,r=!1)=>{let a=U.exec(e);return!!a&&(a[1]?t(a[1]):r)},eP=e=>"position"===e,ez=new Set(["image","url"]),eT=e=>ez.has(e),eC=new Set(["length","size","percentage"]),eE=e=>eC.has(e),eM=e=>"length"===e,eG=e=>"number"===e,eS=e=>"family-name"===e,e_=e=>"shadow"===e;Symbol.toStringTag;let eR=function(e,...t){let r,a,i;let n=function(s){return a=(r=D(t.reduce((e,t)=>t(e),e()))).cache.get,i=r.cache.set,n=o,o(s)};function o(e){let t=a(e);if(t)return t;let n=B(e,r);return i(e,n),n}return function(){return n(W.apply(null,arguments))}}(()=>{let e=$("color"),t=$("font"),r=$("text"),a=$("font-weight"),i=$("tracking"),n=$("leading"),o=$("breakpoint"),s=$("container"),l=$("spacing"),d=$("radius"),c=$("shadow"),h=$("inset-shadow"),p=$("drop-shadow"),u=$("blur"),m=$("perspective"),g=$("aspect"),f=$("ease"),b=$("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],x=()=>["auto","hidden","clip","visible","scroll"],w=()=>["auto","contain","none"],k=()=>[ev,ep,l],A=()=>[ee,"full","auto",...k()],j=()=>[er,"none","subgrid",ev,ep],N=()=>["auto",{span:["full",er,ev,ep]},ev,ep],I=()=>[er,"auto",ev,ep],P=()=>["auto","min","max","fr",ev,ep],z=()=>["start","end","center","between","around","evenly","stretch","baseline"],T=()=>["start","end","center","stretch"],C=()=>["auto",...k()],E=()=>[ee,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...k()],M=()=>[e,ev,ep],G=()=>[ea,eu],S=()=>["","none","full",d,ev,ep],_=()=>["",et,ey,eu],R=()=>["solid","dashed","dotted","double"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],L=()=>["","none",u,ev,ep],O=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",ev,ep],D=()=>["none",et,ev,ep],F=()=>["none",et,ev,ep],B=()=>[et,ev,ep],W=()=>[ee,"full",...k()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ei],breakpoint:[ei],color:[en],container:[ei],"drop-shadow":[ei],ease:["in","out","in-out"],font:[ec],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ei],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ei],shadow:[ei],spacing:["px",et],text:[ei],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ee,ep,ev,g]}],container:["container"],columns:[{columns:[et,ep,ev,s]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...y(),ep,ev]}],overflow:[{overflow:x()}],"overflow-x":[{"overflow-x":x()}],"overflow-y":[{"overflow-y":x()}],overscroll:[{overscroll:w()}],"overscroll-x":[{"overscroll-x":w()}],"overscroll-y":[{"overscroll-y":w()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[er,"auto",ev,ep]}],basis:[{basis:[ee,"full","auto",s,...k()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[et,ee,"auto","initial","none",ep]}],grow:[{grow:["",et,ev,ep]}],shrink:[{shrink:["",et,ev,ep]}],order:[{order:[er,"first","last","none",ev,ep]}],"grid-cols":[{"grid-cols":j()}],"col-start-end":[{col:N()}],"col-start":[{"col-start":I()}],"col-end":[{"col-end":I()}],"grid-rows":[{"grid-rows":j()}],"row-start-end":[{row:N()}],"row-start":[{"row-start":I()}],"row-end":[{"row-end":I()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":P()}],"auto-rows":[{"auto-rows":P()}],gap:[{gap:k()}],"gap-x":[{"gap-x":k()}],"gap-y":[{"gap-y":k()}],"justify-content":[{justify:[...z(),"normal"]}],"justify-items":[{"justify-items":[...T(),"normal"]}],"justify-self":[{"justify-self":["auto",...T()]}],"align-content":[{content:["normal",...z()]}],"align-items":[{items:[...T(),"baseline"]}],"align-self":[{self:["auto",...T(),"baseline"]}],"place-content":[{"place-content":z()}],"place-items":[{"place-items":[...T(),"baseline"]}],"place-self":[{"place-self":["auto",...T()]}],p:[{p:k()}],px:[{px:k()}],py:[{py:k()}],ps:[{ps:k()}],pe:[{pe:k()}],pt:[{pt:k()}],pr:[{pr:k()}],pb:[{pb:k()}],pl:[{pl:k()}],m:[{m:C()}],mx:[{mx:C()}],my:[{my:C()}],ms:[{ms:C()}],me:[{me:C()}],mt:[{mt:C()}],mr:[{mr:C()}],mb:[{mb:C()}],ml:[{ml:C()}],"space-x":[{"space-x":k()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":k()}],"space-y-reverse":["space-y-reverse"],size:[{size:E()}],w:[{w:[s,"screen",...E()]}],"min-w":[{"min-w":[s,"screen","none",...E()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[o]},...E()]}],h:[{h:["screen",...E()]}],"min-h":[{"min-h":["screen","none",...E()]}],"max-h":[{"max-h":["screen",...E()]}],"font-size":[{text:["base",r,ey,eu]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[a,ev,em]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ea,ep]}],"font-family":[{font:[ex,ep,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,ev,ep]}],"line-clamp":[{"line-clamp":[et,"none",ev,em]}],leading:[{leading:[n,...k()]}],"list-image":[{"list-image":["none",ev,ep]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ev,ep]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:M()}],"text-color":[{text:M()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...R(),"wavy"]}],"text-decoration-thickness":[{decoration:[et,"from-font","auto",ev,eu]}],"text-decoration-color":[{decoration:M()}],"underline-offset":[{"underline-offset":[et,"auto",ev,ep]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ev,ep]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ev,ep]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...y(),ew,eg]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",ek,eh]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},er,ev,ep],radial:["",ev,ep],conic:[er,ev,ep]},eA,ef]}],"bg-color":[{bg:M()}],"gradient-from-pos":[{from:G()}],"gradient-via-pos":[{via:G()}],"gradient-to-pos":[{to:G()}],"gradient-from":[{from:M()}],"gradient-via":[{via:M()}],"gradient-to":[{to:M()}],rounded:[{rounded:S()}],"rounded-s":[{"rounded-s":S()}],"rounded-e":[{"rounded-e":S()}],"rounded-t":[{"rounded-t":S()}],"rounded-r":[{"rounded-r":S()}],"rounded-b":[{"rounded-b":S()}],"rounded-l":[{"rounded-l":S()}],"rounded-ss":[{"rounded-ss":S()}],"rounded-se":[{"rounded-se":S()}],"rounded-ee":[{"rounded-ee":S()}],"rounded-es":[{"rounded-es":S()}],"rounded-tl":[{"rounded-tl":S()}],"rounded-tr":[{"rounded-tr":S()}],"rounded-br":[{"rounded-br":S()}],"rounded-bl":[{"rounded-bl":S()}],"border-w":[{border:_()}],"border-w-x":[{"border-x":_()}],"border-w-y":[{"border-y":_()}],"border-w-s":[{"border-s":_()}],"border-w-e":[{"border-e":_()}],"border-w-t":[{"border-t":_()}],"border-w-r":[{"border-r":_()}],"border-w-b":[{"border-b":_()}],"border-w-l":[{"border-l":_()}],"divide-x":[{"divide-x":_()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":_()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...R(),"hidden","none"]}],"divide-style":[{divide:[...R(),"hidden","none"]}],"border-color":[{border:M()}],"border-color-x":[{"border-x":M()}],"border-color-y":[{"border-y":M()}],"border-color-s":[{"border-s":M()}],"border-color-e":[{"border-e":M()}],"border-color-t":[{"border-t":M()}],"border-color-r":[{"border-r":M()}],"border-color-b":[{"border-b":M()}],"border-color-l":[{"border-l":M()}],"divide-color":[{divide:M()}],"outline-style":[{outline:[...R(),"none","hidden"]}],"outline-offset":[{"outline-offset":[et,ev,ep]}],"outline-w":[{outline:["",et,ey,eu]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",c,ej,eb]}],"shadow-color":[{shadow:M()}],"inset-shadow":[{"inset-shadow":["none",ev,ep,h]}],"inset-shadow-color":[{"inset-shadow":M()}],"ring-w":[{ring:_()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:M()}],"ring-offset-w":[{"ring-offset":[et,eu]}],"ring-offset-color":[{"ring-offset":M()}],"inset-ring-w":[{"inset-ring":_()}],"inset-ring-color":[{"inset-ring":M()}],opacity:[{opacity:[et,ev,ep]}],"mix-blend":[{"mix-blend":[...q(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none",ev,ep]}],blur:[{blur:L()}],brightness:[{brightness:[et,ev,ep]}],contrast:[{contrast:[et,ev,ep]}],"drop-shadow":[{"drop-shadow":["","none",p,ev,ep]}],grayscale:[{grayscale:["",et,ev,ep]}],"hue-rotate":[{"hue-rotate":[et,ev,ep]}],invert:[{invert:["",et,ev,ep]}],saturate:[{saturate:[et,ev,ep]}],sepia:[{sepia:["",et,ev,ep]}],"backdrop-filter":[{"backdrop-filter":["","none",ev,ep]}],"backdrop-blur":[{"backdrop-blur":L()}],"backdrop-brightness":[{"backdrop-brightness":[et,ev,ep]}],"backdrop-contrast":[{"backdrop-contrast":[et,ev,ep]}],"backdrop-grayscale":[{"backdrop-grayscale":["",et,ev,ep]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[et,ev,ep]}],"backdrop-invert":[{"backdrop-invert":["",et,ev,ep]}],"backdrop-opacity":[{"backdrop-opacity":[et,ev,ep]}],"backdrop-saturate":[{"backdrop-saturate":[et,ev,ep]}],"backdrop-sepia":[{"backdrop-sepia":["",et,ev,ep]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":k()}],"border-spacing-x":[{"border-spacing-x":k()}],"border-spacing-y":[{"border-spacing-y":k()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ev,ep]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[et,"initial",ev,ep]}],ease:[{ease:["linear","initial",f,ev,ep]}],delay:[{delay:[et,ev,ep]}],animate:[{animate:["none",b,ev,ep]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,ev,ep]}],"perspective-origin":[{"perspective-origin":O()}],rotate:[{rotate:D()}],"rotate-x":[{"rotate-x":D()}],"rotate-y":[{"rotate-y":D()}],"rotate-z":[{"rotate-z":D()}],scale:[{scale:F()}],"scale-x":[{"scale-x":F()}],"scale-y":[{"scale-y":F()}],"scale-z":[{"scale-z":F()}],"scale-3d":["scale-3d"],skew:[{skew:B()}],"skew-x":[{"skew-x":B()}],"skew-y":[{"skew-y":B()}],transform:[{transform:[ev,ep,"","none","gpu","cpu"]}],"transform-origin":[{origin:O()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:W()}],"translate-x":[{"translate-x":W()}],"translate-y":[{"translate-y":W()}],"translate-z":[{"translate-z":W()}],"translate-none":["translate-none"],accent:[{accent:M()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:M()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ev,ep]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ev,ep]}],fill:[{fill:["none",...M()]}],"stroke-w":[{stroke:[et,ey,eu,em]}],stroke:[{stroke:["none",...M()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}}),eq=((e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return I(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:n}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],a=null==n?void 0:n[e];if(null===t)return null;let o=P(t)||P(a);return i[e][o]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return I(e,o,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:r,className:a,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...n,...s}[t]):({...n,...s})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function eL({className:e,variant:t,size:r,asChild:i=!1,...n}){let o=i?k:"button";return(0,a.jsx)(o,{"data-slot":"button",className:function(...e){return eR(I(e))}(eq({variant:t,size:r,className:e})),...n})}function eO(){return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n(),{href:"/",className:"text-xl font-bold tracking-tighter",children:["Cline",(0,a.jsx)("span",{className:"text-blue-800",children:"Coder"})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-6 text-sm",children:[(0,a.jsx)(n(),{href:"/",className:"text-gray-800 hover:text-gray-600 transition-colors",children:"Home"}),(0,a.jsx)(n(),{href:"/articles/",className:"text-gray-800 transition-colors ",children:"Articles"}),(0,a.jsx)(n(),{href:"/topics/",className:"text-gray-800 hover:text-gray-600 transition-colors",children:"Topics"}),(0,a.jsx)(n(),{href:"/about/",className:"text-gray-800 hover:text-gray-600 transition-colors",children:"About"})]}),(0,a.jsx)(eL,{variant:"outline",className:"border-blue-800 text-blue-800 hover:bg-purple-950 hover:text-white",children:"Github"})]})})}let eD={"evolution-of-gans":{title:"The Evolution of Generative Adversarial Networks: From GAN to StyleGAN-3",date:"May 15, 2023",author:"Dr. Alex Chen",category:"GenAI",readTime:"8 min read",image:"https://images.unsplash.com/photo-1617791160505-6f00504e3519?q=80&w=2000&h=1000&auto=format&fit=crop",content:`
      <p>Generative Adversarial Networks (GANs) have revolutionized the field of artificial intelligence since their introduction by Ian Goodfellow and his colleagues in 2014. These networks consist of two neural networks—a generator and a discriminator—that are trained simultaneously through adversarial training.</p>
      
      <h2>The Original GAN</h2>
      <p>The original GAN architecture introduced a novel approach to generative modeling. The generator network creates samples (such as images), while the discriminator network evaluates them. The generator aims to produce samples that are indistinguishable from real data, while the discriminator aims to correctly identify which samples are real and which are generated.</p>
      
      <p>However, early GANs faced significant challenges, including training instability, mode collapse (where the generator produces limited varieties of samples), and difficulty in generating high-resolution images.</p>
      
      <h2>Progressive GAN: A Step Forward</h2>
      <p>In 2017, researchers at NVIDIA introduced Progressive GAN, which addressed many of the limitations of the original architecture. Progressive GAN employed a training methodology where both the generator and discriminator start with low-resolution images and gradually add layers that deal with higher-resolution details.</p>
      
      <p>This progressive training approach significantly improved training stability and enabled the generation of higher-resolution images (up to 1024\xd71024 pixels) with impressive detail and realism.</p>
      
      <h2>StyleGAN: Controlling Image Synthesis</h2>
      <p>Building upon Progressive GAN, NVIDIA researchers introduced StyleGAN in 2018. StyleGAN incorporated a style-based generator architecture that offered unprecedented control over the generated images' features. It separated high-level attributes (such as pose and face shape) from stochastic variations (such as freckles and hair details).</p>
      
      <p>StyleGAN introduced several key innovations:</p>
      <ul>
        <li>A mapping network that transforms the input latent code into an intermediate latent space</li>
        <li>Adaptive instance normalization (AdaIN) to control the style at each convolution layer</li>
        <li>Stochastic variation injection to add randomness to the generated images</li>
      </ul>
      
      <h2>StyleGAN-2: Refining the Architecture</h2>
      <p>In 2020, NVIDIA released StyleGAN-2, which addressed several artifacts present in the original StyleGAN, such as "blob" artifacts and water-like features. StyleGAN-2 redesigned the normalization, regularization, and progressive growing components, resulting in significantly improved image quality.</p>
      
      <p>Key improvements in StyleGAN-2 included:</p>
      <ul>
        <li>Redesigned normalization technique</li>
        <li>Path length regularization</li>
        <li>No progressive growing (replaced with a residual network design)</li>
      </ul>
      
      <h2>StyleGAN-3: Addressing Aliasing</h2>
      <p>The latest iteration, StyleGAN-3 (2021), focuses on eliminating "texture sticking," a phenomenon where texture features remain fixed to image coordinates rather than moving naturally with objects. This was achieved by redesigning the architecture to be more translation and rotation equivariant.</p>
      
      <p>StyleGAN-3 introduces:</p>
      <ul>
        <li>Alias-free generative networks</li>
        <li>Fourier features for improved equivariance</li>
        <li>Filtered non-linearities to prevent aliasing</li>
      </ul>
      
      <h2>Impact and Applications</h2>
      <p>The evolution of GANs from the original architecture to StyleGAN-3 has enabled numerous applications:</p>
      <ul>
        <li>Photorealistic image generation</li>
        <li>Image-to-image translation</li>
        <li>Face editing and manipulation</li>
        <li>Virtual try-on systems</li>
        <li>Data augmentation for training other AI models</li>
      </ul>
      
      <h2>Future Directions</h2>
      <p>As GAN technology continues to evolve, we can expect further improvements in areas such as:</p>
      <ul>
        <li>Multi-modal generation (combining text, image, and other modalities)</li>
        <li>Improved control over generated content</li>
        <li>Reduced computational requirements</li>
        <li>Better integration with other AI techniques</li>
      </ul>
      
      <p>The journey from GAN to StyleGAN-3 represents a remarkable progression in generative modeling, enabling increasingly realistic and controllable image synthesis. As these technologies continue to mature, they will undoubtedly open new possibilities across various domains, from entertainment and art to healthcare and scientific visualization.</p>
    `,relatedPosts:[{title:"The Rise of Multimodal AI Models: Bridging Text, Image, and Beyond",category:"AI Research",image:"https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=600&h=400&auto=format&fit=crop",slug:"multimodal-ai-models"},{title:"AI in 2025: Transforming Daily Life",category:"Future Tech",image:"https://images.unsplash.com/photo-1531746790731-6c087fecd65a?q=80&w=600&h=400&auto=format&fit=crop",slug:"ai-in-2025"}]},"multimodal-ai-models":{title:"The Rise of Multimodal AI Models: Bridging Text, Image, and Beyond",date:"February 5, 2024",author:"Dr. Michael Zhang",category:"AI Research",readTime:"9 min read",image:"https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=2000&h=1000&auto=format&fit=crop",content:`
      <p>Artificial intelligence has undergone a remarkable evolution in recent years, with one of the most significant developments being the rise of multimodal AI models. These sophisticated systems can process, understand, and generate content across multiple types of data—or modalities—such as text, images, audio, and video.</p>
      
      <h2>Understanding Multimodal AI</h2>
      <p>Traditional AI models were typically designed to work with a single type of data. Text-based models like GPT processed and generated language, while image-based models like DALL-E created visual content. These single-modality models, while powerful in their domains, were limited by their inability to connect concepts across different types of information.</p>
      
      <p>Multimodal AI models break down these barriers by integrating multiple types of data into a unified system. They can understand the relationships between text and images, audio and video, or any combination of modalities.</p>
      
      <h2>Key Multimodal AI Models</h2>
      <p>Several groundbreaking multimodal AI models have emerged in recent years:</p>
      
      <ul>
        <li><strong>GPT-4V</strong>: Building on the language capabilities of GPT-4, this model can process both text and images</li>
        <li><strong>CLIP</strong>: Developed by OpenAI, CLIP learns visual concepts from natural language supervision</li>
        <li><strong>DALL-E 3</strong>: This model generates highly detailed and accurate images from text prompts</li>
        <li><strong>Flamingo</strong>: Google DeepMind's model can process interleaved text and images</li>
        <li><strong>AudioLM and MusicLM</strong>: These models bridge text and audio, generating realistic speech or music</li>
      </ul>
      
      <h2>Technical Foundations</h2>
      <p>The development of multimodal AI has been enabled by several technical innovations:</p>
      
      <p><strong>Transformer Architecture</strong>: Originally developed for natural language processing, transformers have proven remarkably adaptable to other modalities.</p>
      
      <p><strong>Joint Embeddings</strong>: Multimodal models create unified representations that capture the meaning of content across different modalities in a shared mathematical space.</p>
      
      <p><strong>Contrastive Learning</strong>: This training approach helps models learn the relationships between different modalities.</p>
      
      <h2>Applications of Multimodal AI</h2>
      <p>The ability to process multiple types of data has opened up numerous applications across various industries:</p>
      
      <h3>Content Creation and Editing</h3>
      <p>Multimodal AI is revolutionizing creative workflows by enabling text-to-image generation, automatic video captioning, and sophisticated editing tools.</p>
      
      <h3>Accessibility</h3>
      <p>These models are making digital content more accessible by automatically generating alternative text for images, creating captions for videos, and translating content between modalities.</p>
      
      <h3>Healthcare</h3>
      <p>In medical settings, multimodal AI can analyze patient data across different formats to assist in diagnosis, treatment planning, and monitoring.</p>
      
      <h2>Challenges and Future Directions</h2>
      <p>Despite their impressive capabilities, multimodal AI models face several challenges including computational requirements, data quality and bias, and alignment between modalities.</p>
      
      <p>As research in this field continues to advance, we can expect more modalities to be incorporated, deeper cross-modal understanding, and integration with robotics to allow multimodal AI to interact with the physical world.</p>
    `,relatedPosts:[{title:"The Evolution of Generative Adversarial Networks: From GAN to StyleGAN-3",category:"GenAI",image:"https://images.unsplash.com/photo-1617791160505-6f00504e3519?q=80&w=600&h=400&auto=format&fit=crop",slug:"evolution-of-gans"},{title:"Deep Learning for Natural Language Processing",category:"NLP",image:"https://images.unsplash.com/photo-**********-bb4caa6b424d?q=80&w=600&h=400&auto=format&fit=crop",slug:"deep-learning-nlp"}]},"ai-in-2025":{title:"AI in 2025: Transforming Daily Life",date:"October 18, 2023",author:"Dr. Sarah Johnson",category:"Future Tech",readTime:"7 min read",image:"https://images.unsplash.com/photo-1531746790731-6c087fecd65a?q=80&w=2000&h=1000&auto=format&fit=crop",content:`
      <p>As we approach 2025, artificial intelligence has become deeply integrated into our daily lives in ways that were once the realm of science fiction. From personal assistants that anticipate our needs to AI systems that help us make better decisions, the technology has transformed how we live, work, and interact with the world around us.</p>
      
      <h2>Personal AI Assistants: Beyond Voice Commands</h2>
      <p>Personal AI assistants have evolved far beyond simple voice-activated helpers. In 2025, these systems understand context, remember past interactions, and proactively offer assistance based on your habits, preferences, and current situation.</p>
      
      <p>These assistants have become truly personal, adapting to individual communication styles and preferences. They can manage complex tasks like negotiating appointment times with other AI assistants, researching and summarizing information across multiple sources, and even handling routine correspondence in your personal communication style.</p>
      
      <h2>AI in Healthcare: Personalized and Preventative</h2>
      <p>Healthcare has been revolutionized by AI's ability to process vast amounts of medical data and identify patterns invisible to human practitioners. By 2025, AI systems routinely analyze data from wearable devices to detect potential health issues before symptoms appear.</p>
      
      <p>Personalized treatment plans, tailored to an individual's genetic makeup, lifestyle, and medical history, have become standard. AI systems can predict how patients will respond to specific medications or treatments, reducing trial and error in healthcare.</p>
      
      <h2>AI in Education: Personalized Learning Journeys</h2>
      <p>Education in 2025 has been transformed by AI systems that adapt to each student's learning style, pace, and interests. These systems identify knowledge gaps, suggest appropriate resources, and adjust difficulty levels in real-time to keep students engaged and challenged without becoming frustrated.</p>
      
      <h2>AI in the Workplace: Augmenting Human Capabilities</h2>
      <p>In the workplace, AI has become an indispensable partner, handling routine tasks and augmenting human capabilities. AI systems analyze data, generate reports, schedule meetings, and even draft correspondence, allowing workers to focus on creative problem-solving, strategic thinking, and interpersonal relationships.</p>
      
      <h2>Challenges and Considerations</h2>
      <p>Despite the benefits, the integration of AI into daily life has not been without challenges. Privacy concerns, algorithmic bias, and the digital divide remain significant issues. Ensuring that AI systems respect user privacy, make fair and unbiased decisions, and are accessible to all segments of society requires ongoing attention and effort.</p>
      
      <h2>Conclusion</h2>
      <p>As we navigate this AI-enhanced world of 2025, the technology continues to evolve, becoming more sophisticated, more intuitive, and more integrated into the fabric of daily life. The most successful AI implementations are those that complement human strengths, handle routine tasks, and provide insights and assistance while allowing humans to focus on what they do best.</p>
    `,relatedPosts:[{title:"The Future of AI Research: What's Next?",category:"Future of AI",image:"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=600&h=400&auto=format&fit=crop",slug:"future-of-ai-research"},{title:"Ethical Considerations in Generative AI",category:"AI Ethics",image:"https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?q=80&w=600&h=400&auto=format&fit=crop",slug:"ethical-considerations-genai"}]},"deep-learning-nlp":{title:"Deep Learning for Natural Language Processing",date:"November 7, 2024",author:"Dr. Lisa Park",category:"NLP",readTime:"9 min read",image:"https://images.unsplash.com/photo-**********-bb4caa6b424d?q=80&w=2000&h=1000&auto=format&fit=crop",content:`
      <p>Natural Language Processing (NLP) has undergone a revolutionary transformation in recent years, driven largely by advances in deep learning. These powerful neural network approaches have dramatically improved machines' ability to understand, generate, and interact with human language.</p>
      
      <h2>The Evolution of NLP: From Rules to Neural Networks</h2>
      <p>To appreciate the impact of deep learning on NLP, it's helpful to understand how the field has evolved:</p>
      
      <h3>Rule-Based Systems (1950s-1980s)</h3>
      <p>Early NLP systems relied on hand-crafted rules and linguistic knowledge. While these approaches could handle specific, well-defined tasks, they struggled with language's inherent ambiguity.</p>
      
      <h3>Statistical Methods (1990s-2000s)</h3>
      <p>The next wave of NLP introduced statistical approaches like Hidden Markov Models and Conditional Random Fields. These methods learned patterns from data rather than relying solely on explicit rules.</p>
      
      <h3>The Transformer Revolution (2017-Present)</h3>
      <p>The introduction of the Transformer architecture in 2017 marked a watershed moment for NLP. Unlike previous approaches, Transformers process entire sequences in parallel using attention mechanisms, addressing limitations in handling long-range dependencies.</p>
      
      <h2>Key Deep Learning Architectures for NLP</h2>
      <p>Several neural network architectures have proven particularly effective for NLP tasks:</p>
      
      <h3>Transformer Models</h3>
      <p>The Transformer architecture has become the dominant approach in modern NLP, featuring self-attention mechanisms, parallelization, and excellent scalability.</p>
      
      <h3>Pre-trained Language Models</h3>
      <p>Building on the Transformer architecture, pre-trained language models like BERT, GPT, and T5 have revolutionized NLP by learning from vast amounts of text data before being fine-tuned for specific tasks.</p>
      
      <h2>Applications of Deep Learning in NLP</h2>
      <p>Deep learning has transformed numerous NLP applications including machine translation, conversational AI, content generation, and information extraction and retrieval.</p>
      
      <h2>Challenges and Future Directions</h2>
      <p>Despite remarkable progress, deep learning approaches to NLP face several challenges including computational requirements, data needs, reliability issues, and ethical considerations.</p>
      
      <p>Promising research directions include more efficient models, retrieval-augmented generation, improved reasoning capabilities, and deeper integration with other modalities.</p>
    `,relatedPosts:[{title:"The Rise of Multimodal AI Models: Bridging Text, Image, and Beyond",category:"AI Research",image:"https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=600&h=400&auto=format&fit=crop",slug:"multimodal-ai-models"},{title:"Ethical Considerations in Generative AI",category:"AI Ethics",image:"https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?q=80&w=600&h=400&auto=format&fit=crop",slug:"ethical-considerations-genai"}]},"future-of-ai-research":{title:"The Future of AI Research: What's Next?",date:"February 28, 2025",author:"Dr. Thomas Anderson",category:"Future of AI",readTime:"10 min read",image:"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=2000&h=1000&auto=format&fit=crop",content:`
      <p>Artificial intelligence has advanced at a breathtaking pace in recent years, with breakthroughs in areas like large language models, diffusion-based image generation, and multimodal systems transforming what we thought possible. As we look to the future of AI research, several promising directions are emerging.</p>
      
      <h2>Beyond Scale: New Paradigms in AI Architecture</h2>
      <p>While scaling neural networks to unprecedented sizes has driven many recent advances, researchers are increasingly exploring alternative approaches:</p>
      
      <h3>Modular and Compositional Architectures</h3>
      <p>Rather than monolithic models, future AI systems may consist of specialized modules that can be dynamically composed, including Mixture of Experts (MoE) models, neural symbolic integration, and modular training approaches.</p>
      
      <h3>Self-Supervised and Unsupervised Learning</h3>
      <p>Moving beyond supervised learning with labeled data, researchers are developing more sophisticated approaches to learning from unlabeled data, such as contrastive learning, masked prediction, and energy-based models.</p>
      
      <h2>Embodied AI and Robotics</h2>
      <p>Moving beyond disembodied models that process text or images, researchers are increasingly focusing on AI systems that can interact with the physical world:</p>
      
      <h3>Physical Grounding</h3>
      <p>Embodied AI research explores how physical interaction shapes intelligence through sensorimotor learning, multimodal integration, and affordance learning.</p>
      
      <h3>Human-Robot Collaboration</h3>
      <p>Rather than fully autonomous systems, many researchers are focusing on robots that can work alongside humans with intuitive interfaces, shared autonomy, and adaptive assistance.</p>
      
      <h2>AI for Scientific Discovery</h2>
      <p>AI is increasingly being applied to accelerate scientific research across disciplines through automated experimentation, scientific foundation models, and advanced simulation and modeling.</p>
      
      <h2>Human-AI Collaboration and Augmentation</h2>
      <p>Beyond autonomous systems, researchers are exploring how AI can enhance human capabilities through cognitive augmentation, interpretable AI, and adaptive interfaces.</p>
      
      <h2>Ethical and Responsible AI</h2>
      <p>As AI becomes more powerful, ensuring it is developed and deployed responsibly becomes increasingly important, with research focusing on AI alignment, fairness and bias mitigation, and governance frameworks.</p>
    `,relatedPosts:[{title:"Ethical Considerations in Generative AI",category:"AI Ethics",image:"https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?q=80&w=600&h=400&auto=format&fit=crop",slug:"ethical-considerations-genai"},{title:"AI in 2025: Transforming Daily Life",category:"Future Tech",image:"https://images.unsplash.com/photo-1531746790731-6c087fecd65a?q=80&w=600&h=400&auto=format&fit=crop",slug:"ai-in-2025"}]},"ethical-considerations-genai":{title:"Ethical Considerations in Generative AI",date:"January 14, 2025",author:"Dr. Maya Patel",category:"AI Ethics",readTime:"8 min read",image:"https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?q=80&w=2000&h=1000&auto=format&fit=crop",content:`
      <p>Generative AI has emerged as one of the most transformative technologies of our time, capable of creating text, images, audio, video, and code that increasingly resembles human-created content. While these capabilities offer tremendous potential, they also raise profound ethical questions.</p>
      
      <h2>Understanding Generative AI</h2>
      <p>Generative AI refers to artificial intelligence systems that can create new content rather than simply analyzing or categorizing existing data. Modern generative AI systems have demonstrated remarkable capabilities in generating human-like text, creating photorealistic images, producing music and voice recordings, writing functional computer code, and translating between languages.</p>
      
      <h2>Key Ethical Considerations</h2>
      
      <h3>1. Bias and Fairness</h3>
      <p>Generative AI systems learn from existing data, which inevitably contains societal biases. This raises concerns about amplification of existing biases, representation disparities, and potential discriminatory outcomes.</p>
      
      <h3>2. Misinformation and Manipulation</h3>
      <p>The ability to generate convincing content raises concerns about deepfakes and synthetic media, automated disinformation, and personalized manipulation.</p>
      
      <h3>3. Intellectual Property and Attribution</h3>
      <p>Generative AI raises complex questions about training data rights, output ownership, and impacts on creative labor.</p>
      
      <h3>4. Privacy and Consent</h3>
      <p>These systems raise several privacy concerns including training data privacy, synthetic identity creation, and enhanced surveillance capabilities.</p>
      
      <h2>Ethical Frameworks and Governance Approaches</h2>
      <p>Addressing these ethical considerations requires multifaceted approaches including technical solutions like alignment techniques and safety measures, policy and regulatory approaches, responsible organizational practices, and individual and collective responsibility.</p>
      
      <h2>The Path Forward</h2>
      <p>As generative AI continues to advance, several principles can guide ethical development and deployment including anticipatory governance, shared responsibility across sectors, and human-centered design that augments human capabilities rather than replacing human agency.</p>
    `,relatedPosts:[{title:"The Future of AI Research: What's Next?",category:"Future of AI",image:"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=600&h=400&auto=format&fit=crop",slug:"future-of-ai-research"},{title:"Deep Learning for Natural Language Processing",category:"NLP",image:"https://images.unsplash.com/photo-**********-bb4caa6b424d?q=80&w=600&h=400&auto=format&fit=crop",slug:"deep-learning-nlp"}]},"ai-regulation-landscape-2025":{title:"AI Regulation Landscape in 2025: Global Policies and Industry Impact",date:"March 1, 2025",author:"Dr. Elena Kowalski",category:"AI Policy",readTime:"11 min read",image:"https://images.unsplash.com/photo-1589254065909-b7086229d08c?q=80&w=2000&h=1000&auto=format&fit=crop",content:`
      <p>The regulatory landscape for artificial intelligence has evolved dramatically over the past few years, as governments and international bodies have worked to establish frameworks that balance innovation with safety, privacy, and ethical considerations.</p>
      
      <h2>Major Regulatory Frameworks</h2>
      <p>Several jurisdictions have introduced comprehensive AI regulations aimed at ensuring responsible development and deployment of AI technologies:</p>
      
      <h3>1. The European Union: AI Act</h3>
      <p>The EU AI Act classifies AI systems into risk categories—unacceptable, high-risk, and low-risk—and imposes varying levels of regulatory scrutiny based on these classifications.</p>
      
      <h3>2. United States: AI Bill of Rights and Executive Orders</h3>
      <p>The U.S. has taken a sectoral approach to AI regulation, with federal guidelines emphasizing transparency, fairness, and human oversight in AI applications.</p>
      
      <h3>3. China: AI Governance and Social Stability</h3>
      <p>China has introduced strict AI governance policies, particularly targeting deepfakes, algorithmic recommendations, and large-scale AI deployments.</p>
      
      <h3>4. Global AI Standards and International Cooperation</h3>
      <p>Organizations like the OECD, United Nations, and G7 have worked to establish global AI governance principles emphasizing transparency, fairness, and international cooperation.</p>
      
      <h2>Industry Adaptation and Compliance</h2>
      <p>AI companies have been adjusting their policies, development practices, and risk management strategies to comply with new regulations through responsible AI initiatives and increased focus on explainability.</p>
      
      <h2>Challenges and Future Outlook</h2>
      <p>Despite progress, several challenges remain including regulatory fragmentation across jurisdictions, enforcement complexity, and the ongoing challenge of balancing innovation with responsible deployment.</p>
      
      <p>As AI continues to evolve, regulatory frameworks will need to adapt to emerging risks and opportunities, requiring collaboration between governments, industry leaders, and researchers to ensure AI develops in a way that is both ethical and beneficial to society.</p>
    `,relatedPosts:[{title:"The Future of AI Research: What's Next?",category:"Future of AI",image:"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=600&h=400&auto=format&fit=crop",slug:"future-of-ai-research"},{title:"Ethical Considerations in Generative AI",category:"AI Ethics",image:"https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?q=80&w=600&h=400&auto=format&fit=crop",slug:"ethical-considerations-genai"}]}};async function eF({params:e}){let{slug:t}=await e,r=eD[t];return r?(0,a.jsxs)("div",{className:"min-h-scree",children:[(0,a.jsx)("header",{className:"container mx-auto py-6",children:(0,a.jsx)(eO,{})}),(0,a.jsx)("main",{className:"container mx-auto px-4 py-12",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,a.jsxs)(n(),{href:"/articles/",className:"inline-flex items-center text-gray-400 hover:text-white mb-8",children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-2"}),"Back to articles"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-blue-800 mb-4",children:[(0,a.jsx)(g,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:r.category})]}),(0,a.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-6",children:r.title}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-400 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(f,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:r.readTime})]}),(0,a.jsx)("div",{children:r.date}),(0,a.jsxs)("div",{children:["By ",r.author]})]}),(0,a.jsx)("div",{className:"relative h-[400px] md:h-[500px] rounded-xl overflow-hidden border border-gray-800 mb-8",children:(0,a.jsx)(s(),{src:r.image||"/placeholder.svg",alt:"Article hero image showing GAN-generated art",fill:!0,className:"object-cover",priority:!0})}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(eL,{variant:"outline",size:"sm",className:"h-8 px-3 border-gray-800 hover:bg-gray-800 hover:text-white",children:[(0,a.jsx)(b,{className:"h-4 w-4 mr-1"}),"Share"]}),(0,a.jsxs)(eL,{variant:"outline",size:"sm",className:"h-8 px-3 border-gray-800 hover:bg-gray-900 hover:text-white",children:[(0,a.jsx)(v,{className:"h-4 w-4 mr-1"}),"Share"]}),(0,a.jsxs)(eL,{variant:"outline",size:"sm",className:"h-8 px-3 border-gray-800 hover:bg-gray-900 hover:text-white",children:[(0,a.jsx)(y,{className:"h-4 w-4 mr-1"}),"Share"]})]}),(0,a.jsxs)(eL,{variant:"outline",size:"sm",className:"h-8 px-3 border-gray-800 hover:bg-gray-900 hover:text-white",children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-1"}),"Share"]})]}),(0,a.jsx)("article",{className:"prose prose-invert prose-purple max-w-none text-xl/7",children:(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:r.content}})}),(0,a.jsxs)("div",{className:"border-t border-gray-800 mt-12 pt-8",children:[(0,a.jsx)("h3",{className:"text-xl font-bold mb-6",children:"Related Articles"}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:(0,a.jsx)(n(),{href:"/blog/11}/",className:"group",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"relative h-48 rounded-lg overflow-hidden border border-gray-800 group-hover:border-blue-800/50 transition-colors",children:(0,a.jsx)(s(),{src:"/placeholder.svg",alt:"thumbnail",fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-blue-800 mb-2",children:[(0,a.jsx)(g,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"c"})]}),(0,a.jsx)("h3",{className:"font-medium group-hover:text-blue-400 transition-colors",children:"11"})]})]})})})]})]})}),(0,a.jsx)("footer",{className:"border-t border-gray-800 py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,a.jsxs)(n(),{href:"/",className:"text-xl font-bold tracking-tighter",children:["Cline",(0,a.jsx)("span",{className:"text-blue-800",children:"Coder"})]}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-4 mb-6",children:"Exploring the cutting edge of artificial intelligence and machine learning."}),(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,a.jsx)(n(),{href:"#",className:"text-gray-400 hover:text-white",children:(0,a.jsx)(b,{className:"h-5 w-5"})}),(0,a.jsx)(n(),{href:"#",className:"text-gray-400 hover:text-white",children:(0,a.jsx)(v,{className:"h-5 w-5"})}),(0,a.jsx)(n(),{href:"#",className:"text-gray-400 hover:text-white",children:(0,a.jsx)(y,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-6 text-sm text-gray-400",children:(0,a.jsx)("p",{children:"\xa9 2025 ClineCoder. All rights reserved."})})]})})})]}):(0,a.jsx)("div",{className:"min-h-screen bg-black text-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"Post Not Found"}),(0,a.jsx)("p",{className:"mb-6",children:"The blog post you're looking for doesn't exist or has been moved."}),(0,a.jsx)(eL,{asChild:!0,children:(0,a.jsx)(n(),{href:"/",children:"Return Home"})})]})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[283,739,837,47],()=>r(1674));module.exports=a})();