{
  "$schema": "node_modules/wrangler/config-schema.json",
  "main": ".open-next/worker.js",
  "name": "clinecoder",
  "compatibility_date": "2025-06-14",
  "compatibility_flags": ["nodejs_compat", "global_fetch_strictly_public"],
  "assets": {
    "directory": ".open-next/assets",
    "binding": "ASSETS"
  },
  "r2_buckets": [
    // Use R2 incremental cache
    // See https://opennext.js.org/cloudflare/caching
    {
      "binding": "NEXT_INC_CACHE_R2_BUCKET",
      // Create the bucket before deploying
      // You can change the bucket name if you want
      // See https://developers.cloudflare.com/workers/wrangler/commands/#r2-bucket-create
      "bucket_name": "cache"
    },
    // R2 bucket for blog images
    {
      "binding": "bucket",
      "bucket_name": "clinecoder"
    }
  ]
}
