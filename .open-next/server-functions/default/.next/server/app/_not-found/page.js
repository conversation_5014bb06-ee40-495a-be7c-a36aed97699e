(()=>{var e={};e.id=492,e.ids=[492],e.modules={392:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>p,routeModule:()=>u,tree:()=>l});var o=t(8773),n=t(8018),s=t(4828),i=t.n(s),d=t(6395),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,2264,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1638)),"/home/<USER>/projects/clinecoder.com/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,2264,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,1121,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5678,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=[],m={require:t,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},900:(e,r,t)=>{Promise.resolve().then(t.bind(t,9273))},1638:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,metadata:()=>d});var o=t(6147);t(9110);var n=t(3432),s=t.n(n),i=t(2579);let d={title:"Cline coder",description:"Exploring the frontiers of artificial intelligence, generative AI, computer vision, and deep learning."};function a({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsxs)("body",{className:s().className,children:[e,(0,o.jsx)(i.Toaster,{})]})})}},2579:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>o});let o=(0,t(1109).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/projects/clinecoder.com/components/ui/sonner.tsx","Toaster")},2634:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4126,23)),Promise.resolve().then(t.t.bind(t,7432,23)),Promise.resolve().then(t.t.bind(t,4828,23)),Promise.resolve().then(t.t.bind(t,1215,23)),Promise.resolve().then(t.t.bind(t,2375,23)),Promise.resolve().then(t.t.bind(t,2271,23)),Promise.resolve().then(t.t.bind(t,3327,23)),Promise.resolve().then(t.t.bind(t,1801,23))},2802:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,8016,23)),Promise.resolve().then(t.t.bind(t,1918,23)),Promise.resolve().then(t.t.bind(t,8654,23)),Promise.resolve().then(t.t.bind(t,8305,23)),Promise.resolve().then(t.t.bind(t,6405,23)),Promise.resolve().then(t.t.bind(t,7709,23)),Promise.resolve().then(t.t.bind(t,3813,23)),Promise.resolve().then(t.t.bind(t,9407,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},8164:(e,r,t)=>{Promise.resolve().then(t.bind(t,2579))},9110:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9273:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>i});var o=t(5921),n=t(4750),s=t(6737);let i=({...e})=>{let{theme:r="system"}=(0,n.D)();return(0,o.jsx)(s.l$,{theme:r,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[283,739],()=>t(392));module.exports=o})();