# 博客管理功能使用指南

## 功能概述

本项目已集成完整的博客管理功能，包括：

- ✅ 博客增删改查 (CRUD)
- ✅ 富文本编辑器 (Tiptap)
- ✅ 图片上传到 R2 存储
- ✅ 响应式管理界面
- ✅ 数据库集成 (Prisma + PostgreSQL)

## 访问管理界面

1. **博客列表管理**: `/admin/blogs`
2. **创建新博客**: `/admin/blogs/new`
3. **编辑博客**: `/admin/blogs/[id]/edit`

## 功能特性

### 富文本编辑器
- 支持标题 (H1, H2, H3)
- 文本格式化 (粗体、斜体、下划线、删除线)
- 列表 (有序、无序)
- 引用块
- 代码块
- 链接插入
- 图片上传和插入
- 文本对齐
- 撤销/重做

### 图片上传
- 支持格式: JPEG, PNG, GIF, WebP
- 最大文件大小: 5MB
- 自动上传到 Cloudflare R2
- 拖拽上传支持

### 博客管理
- 创建新博客文章
- 编辑现有文章
- 删除文章
- 预览文章
- 分页浏览
- 自动生成 URL slug

## 配置要求

### 1. 数据库设置
确保 Prisma 数据库已正确配置并迁移：

```bash
npx prisma migrate dev
npx prisma generate
```

### 2. Cloudflare R2 配置

#### 创建 R2 存储桶
```bash
# 创建缓存存储桶 (如果还没有)
wrangler r2 bucket create cache

# 创建博客图片存储桶
wrangler r2 bucket create blog-images
```

#### 配置公共访问 (可选)
如果需要公共访问图片，可以配置自定义域名或使用 R2 的公共 URL。

### 3. 环境变量
确保以下环境变量已配置：

```env
DATABASE_URL="your-database-url"
```

### 4. Wrangler 配置
`wrangler.jsonc` 文件已配置了必要的 R2 绑定：

```json
{
  "r2_buckets": [
    {
      "binding": "NEXT_INC_CACHE_R2_BUCKET",
      "bucket_name": "cache"
    },
    {
      "binding": "BLOG_IMAGES_R2_BUCKET",
      "bucket_name": "blog-images"
    }
  ]
}
```

## API 端点

### 博客 API
- `GET /api/blogs` - 获取博客列表 (支持分页)
- `POST /api/blogs` - 创建新博客
- `GET /api/blogs/[id]` - 获取单个博客
- `PUT /api/blogs/[id]` - 更新博客
- `DELETE /api/blogs/[id]` - 删除博客

### 上传 API
- `POST /api/upload` - 上传图片到 R2

## 使用流程

### 创建新博客
1. 访问 `/admin/blogs`
2. 点击 "New Blog" 按钮
3. 填写博客信息：
   - 标题 (必填)
   - URL Slug (自动生成，可手动修改)
   - 作者 (必填)
   - 分类 (必填)
   - 阅读时间 (必填)
   - 发布日期 (必填)
   - 特色图片 URL (可选)
   - 内容 (必填，使用富文本编辑器)
4. 点击 "Create Blog" 保存

### 编辑博客
1. 在博客列表中点击编辑按钮
2. 修改需要更新的字段
3. 点击 "Update Blog" 保存更改

### 上传图片
1. 在富文本编辑器中点击图片按钮
2. 拖拽或选择图片文件
3. 图片会自动上传到 R2 并插入到编辑器中

## 部署注意事项

### 本地开发
```bash
npm run dev
```

### 部署到 Cloudflare
```bash
npm run deploy
```

### 数据库迁移
在部署前确保数据库 schema 是最新的：

```bash
npx prisma migrate deploy
```

## 故障排除

### 图片上传失败
1. 检查 R2 存储桶是否已创建
2. 确认 wrangler.jsonc 配置正确
3. 检查文件大小和格式是否符合要求

### 数据库连接问题
1. 确认 DATABASE_URL 环境变量正确
2. 检查数据库服务是否运行
3. 运行 `npx prisma db push` 同步 schema

### 富文本编辑器样式问题
1. 确认 globals.css 中的 Tiptap 样式已加载
2. 检查 Tailwind CSS 配置

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript
- **样式**: Tailwind CSS
- **富文本编辑器**: Tiptap
- **数据库**: PostgreSQL + Prisma ORM
- **文件存储**: Cloudflare R2
- **部署**: Cloudflare Pages/Workers

## 扩展功能建议

1. **用户认证**: 添加管理员登录功能
2. **图片优化**: 自动压缩和格式转换
3. **SEO 优化**: 自动生成 meta 标签
4. **草稿功能**: 支持保存草稿
5. **标签系统**: 为博客添加标签功能
6. **评论系统**: 集成评论功能
7. **搜索功能**: 全文搜索博客内容
