'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Plus, Edit, Trash2, Eye } from 'lucide-react'
import Nav from '@/components/nav'

interface Blog {
  id: string
  title: string
  slug: string
  author: string
  category: string
  date: string
  image: string
  readTime: string
  createdAt: string
  updatedAt: string
}

interface BlogsResponse {
  posts: Blog[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function BlogsAdminPage() {
  const [blogs, setBlogs] = useState<Blog[]>([])
  const [loading, setLoading] = useState(true)
  const [deleting, setDeleting] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  })

  const fetchBlogs = async (page = 1) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/blogs?page=${page}&limit=10`)
      if (!response.ok) throw new Error('Failed to fetch blogs')
      
      const data: BlogsResponse = await response.json()
      setBlogs(data.posts)
      setPagination(data.pagination)
    } catch (error) {
      console.error('Error fetching blogs:', error)
      alert('Failed to fetch blogs')
    } finally {
      setLoading(false)
    }
  }

  const deleteBlog = async (id: string) => {
    if (!confirm('Are you sure you want to delete this blog?')) return

    try {
      setDeleting(id)
      const response = await fetch(`/api/blogs/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) throw new Error('Failed to delete blog')

      setBlogs(blogs.filter(blog => blog.id !== id))
      alert('Blog deleted successfully')
    } catch (error) {
      console.error('Error deleting blog:', error)
      alert('Failed to delete blog')
    } finally {
      setDeleting(null)
    }
  }

  useEffect(() => {
    fetchBlogs()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen">
        <header className="container mx-auto py-6">
          <Nav />
        </header>
        <main className="container mx-auto px-4 py-12">
          <div className="flex items-center justify-center">
            <div className="text-center">Loading...</div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <header className="container mx-auto py-6">
        <Nav />
      </header>

      <main className="container mx-auto px-4 py-12">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Blog Management</h1>
          <Link href="/admin/blogs/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Blog
            </Button>
          </Link>
        </div>

        {blogs.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-400 mb-4">No blogs found</p>
            <Link href="/admin/blogs/new">
              <Button>Create your first blog</Button>
            </Link>
          </div>
        ) : (
          <>
            <div className="grid gap-6">
              {blogs.map((blog) => (
                <div
                  key={blog.id}
                  className="border border-gray-700 rounded-lg p-6 bg-gray-800/50"
                >
                  <div className="flex gap-4">
                    <div className="relative w-32 h-24 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={blog.image || '/placeholder.svg'}
                        alt={blog.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-xl font-semibold">{blog.title}</h3>
                        <div className="flex gap-2">
                          <Link href={`/blog/${blog.slug}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Link href={`/admin/blogs/${blog.id}/edit`}>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteBlog(blog.id)}
                            disabled={deleting === blog.id}
                            className="text-red-400 hover:text-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-400 mb-2">
                        <span className="bg-blue-800 text-blue-200 px-2 py-1 rounded text-xs">
                          {blog.category}
                        </span>
                        <span>By {blog.author}</span>
                        <span>{blog.readTime}</span>
                        <span>{new Date(blog.date).toLocaleDateString()}</span>
                      </div>
                      
                      <div className="text-xs text-gray-500">
                        Created: {new Date(blog.createdAt).toLocaleString()}
                        {blog.updatedAt !== blog.createdAt && (
                          <span className="ml-4">
                            Updated: {new Date(blog.updatedAt).toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex justify-center gap-2 mt-8">
                {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={page === pagination.page ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => fetchBlogs(page)}
                  >
                    {page}
                  </Button>
                ))}
              </div>
            )}
          </>
        )}
      </main>
    </div>
  )
}
