(()=>{var e={};e.id=292,e.ids=[292],e.modules={203:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(2478).A)("BrainCircuit",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M9 13a4.5 4.5 0 0 0 3-4",key:"10igwf"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M12 13h4",key:"1ku699"}],["path",{d:"M12 18h6a2 2 0 0 1 2 2v1",key:"105ag5"}],["path",{d:"M12 8h8",key:"1lhi5i"}],["path",{d:"M16 8V5a2 2 0 0 1 2-2",key:"u6izg6"}],["circle",{cx:"16",cy:"13",r:".5",key:"ry7gng"}],["circle",{cx:"18",cy:"3",r:".5",key:"1aiba7"}],["circle",{cx:"20",cy:"21",r:".5",key:"yhc1fs"}],["circle",{cx:"20",cy:"8",r:".5",key:"1e43v0"}]])},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2018:(e,t,r)=>{Promise.resolve().then(r.bind(r,7015))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3631:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return n}});let i=r(8673),a=r(791),s=r(1047),o=i._(r(8415));function n(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},3873:e=>{"use strict";e.exports=require("path")},4756:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(2478).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4780:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var i=r(3631),a=r.n(i)},6522:(e,t,r)=>{Promise.resolve().then(r.bind(r,8333))},7015:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(1109).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/projects/clinecoder.com/app/articles/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/projects/clinecoder.com/app/articles/page.tsx","default")},8333:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var i=r(5921),a=r(1564),s=r.n(a),o=r(4780),n=r(203),l=r(4756),c=r(4100),d=r(8234);let p=[{title:"The Evolution of Generative Adversarial Networks: From GAN to StyleGAN-3",description:"Explore the development of GAN architectures, highlighting key milestones like Progressive GAN, StyleGAN-1, StyleGAN-2, and the latest advancements in StyleGAN-3.",category:"GenAI",date:"May 15, 2023",slug:"evolution-of-gans",image:"https://images.unsplash.com/photo-1617791160505-6f00504e3519?q=80&w=600&h=400&auto=format&fit=crop"},{title:"AI in 2025: Transforming Daily Life",description:"Discuss how generative AI has integrated into everyday activities by 2025, providing personal style tips, translating conversations, analyzing diets, and more.",category:"Future Tech",date:"June 2, 2023",slug:"ai-in-2025",image:"https://images.unsplash.com/photo-1531746790731-6c087fecd65a?q=80&w=600&h=400&auto=format&fit=crop"},{title:"The Rise of Multimodal AI Models: Bridging Text, Image, and Beyond",description:"Examine the emergence of multimodal AI models that process and generate multiple data types, such as text, images, and videos, and their applications in various industries.",category:"AI Research",date:"June 28, 2023",slug:"multimodal-ai-models",image:"https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=600&h=400&auto=format&fit=crop"},{title:"Advancements in AI-Driven 3D Modeling and Virtual World Creation",description:"Explore how AI is revolutionizing 3D modeling and virtual world creation, enabling users to transform written prompts into immersive experiences.",category:"3D Modeling",date:"July 5, 2023",slug:"ai-driven-3d-modeling",image:"https://images.unsplash.com/photo-1633412802994-5c058f151b66?q=80&w=600&h=400&auto=format&fit=crop"},{title:"The Integration of AI in Wearable Technology: Enhancing User Experience",description:"Analyze the incorporation of AI into wearable devices, such as smart glasses, and how it enhances user interaction through features like real-world navigation and information accessibility.",category:"Wearable Tech",date:"July 18, 2023",slug:"ai-in-wearable-technology",image:"https://images.unsplash.com/photo-1551808525-51a94da548ce?q=80&w=600&h=400&auto=format&fit=crop"},{title:"Computer Vision in Autonomous Vehicles",description:"Exploring how computer vision algorithms are enabling self-driving cars to perceive and navigate complex environments.",category:"Computer Vision",date:"August 3, 2023",slug:"computer-vision-autonomous-vehicles",image:"https://images.unsplash.com/photo-1563630381190-77c336ea545a?q=80&w=600&h=400&auto=format&fit=crop"},{title:"Deep Learning for Natural Language Processing",description:"How transformer models have revolutionized our ability to understand and generate human language.",category:"NLP",date:"August 15, 2023",slug:"deep-learning-nlp",image:"https://images.unsplash.com/photo-1546410531-bb4caa6b424d?q=80&w=600&h=400&auto=format&fit=crop"},{title:"Ethical Considerations in Generative AI",description:"Examining the ethical implications of AI-generated content and the responsibility of AI researchers and practitioners.",category:"AI Ethics",date:"September 2, 2023",slug:"ethical-considerations-genai",image:"https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?q=80&w=600&h=400&auto=format&fit=crop"},{title:"The Future of AI Research: What's Next?",description:"Predictions and insights into the next frontiers of artificial intelligence research and development.",category:"Future of AI",date:"September 20, 2023",slug:"future-of-ai-research",image:"https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=600&h=400&auto=format&fit=crop"}];function u(){return(0,i.jsxs)("div",{className:"min-h-screen",children:[(0,i.jsx)("header",{className:"container mx-auto py-6",children:(0,i.jsx)(c.A,{})}),(0,i.jsx)("main",{className:"container mx-auto px-4 py-12",children:(0,i.jsxs)("section",{className:"mb-12",children:[(0,i.jsx)("h1",{className:"text-4xl font-bold mb-8",children:"All Articles"}),(0,i.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map((e,t)=>(0,i.jsx)(m,{title:e.title,description:e.description,category:e.category,date:e.date,slug:e.slug,image:e.image},t))})]})}),(0,i.jsx)("footer",{className:"border-t border-gray-800 py-12",children:(0,i.jsx)(d.A,{})})]})}function m({title:e,description:t,category:r,date:a,slug:c="",image:d}){return(0,i.jsx)(s(),{href:`/blog/${c}/`,className:"group",children:(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("div",{className:"relative h-48 rounded-lg overflow-hidden border border-gray-800 group-hover:border-blue-800/50 transition-colors",children:(0,i.jsx)(o.default,{src:d||"/placeholder.svg",alt:`${e} thumbnail`,fill:!0,className:"object-cover"})}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 text-xs text-blue-800 mb-2",children:[(0,i.jsx)(n.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:r})]}),(0,i.jsx)("h3",{className:"font-medium group-hover:text-blue-400 transition-colors",children:e}),(0,i.jsx)("p",{className:"text-gray-400 text-sm mt-2 line-clamp-2",children:t}),(0,i.jsxs)("div",{className:"flex items-center gap-1 mt-3 text-xs text-gray-500",children:[(0,i.jsx)(l.A,{className:"h-3 w-3"}),(0,i.jsx)("span",{children:a})]})]})]})})}},8346:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var i=r(8773),a=r(8018),s=r(4828),o=r.n(s),n=r(6395),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["articles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7015)),"/home/<USER>/projects/clinecoder.com/app/articles/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,7765))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1638)),"/home/<USER>/projects/clinecoder.com/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,2264,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,1121,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5678,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,7765))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/home/<USER>/projects/clinecoder.com/app/articles/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/articles/page",pathname:"/articles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[283,739,837,506,47,622],()=>r(8346));module.exports=i})();