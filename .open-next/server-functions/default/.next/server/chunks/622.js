exports.id=622,exports.ids=[622],exports.modules={900:(e,s,r)=>{Promise.resolve().then(r.bind(r,9273))},1054:(e,s,r)=>{"use strict";r.d(s,{$:()=>o});var t=r(5921);r(7028);var i=r(9369),n=r(8678),a=r(6841);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:s,size:r,asChild:n=!1,...o}){let c=n?i.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,a.cn)(l({variant:s,size:r,className:e})),...o})}},1638:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o,metadata:()=>l});var t=r(6147);r(9110);var i=r(3432),n=r.n(i),a=r(2579);let l={title:"Cline coder",description:"Exploring the frontiers of artificial intelligence, generative AI, computer vision, and deep learning."};function o({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsxs)("body",{className:n().className,children:[e,(0,t.jsx)(a.Toaster,{})]})})}},2579:(e,s,r)=>{"use strict";r.d(s,{Toaster:()=>t});let t=(0,r(1109).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/projects/clinecoder.com/components/ui/sonner.tsx","Toaster")},2634:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4126,23)),Promise.resolve().then(r.t.bind(r,7432,23)),Promise.resolve().then(r.t.bind(r,4828,23)),Promise.resolve().then(r.t.bind(r,1215,23)),Promise.resolve().then(r.t.bind(r,2375,23)),Promise.resolve().then(r.t.bind(r,2271,23)),Promise.resolve().then(r.t.bind(r,3327,23)),Promise.resolve().then(r.t.bind(r,1801,23))},2802:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,8016,23)),Promise.resolve().then(r.t.bind(r,1918,23)),Promise.resolve().then(r.t.bind(r,8654,23)),Promise.resolve().then(r.t.bind(r,8305,23)),Promise.resolve().then(r.t.bind(r,6405,23)),Promise.resolve().then(r.t.bind(r,7709,23)),Promise.resolve().then(r.t.bind(r,3813,23)),Promise.resolve().then(r.t.bind(r,9407,23))},4100:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});var t=r(5921),i=r(1564),n=r.n(i),a=r(1054);function l(){return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(n(),{href:"/",className:"text-xl font-bold tracking-tighter",children:["Cline",(0,t.jsx)("span",{className:"text-blue-800",children:"Coder"})]}),(0,t.jsxs)("nav",{className:"hidden md:flex items-center space-x-6 text-sm",children:[(0,t.jsx)(n(),{href:"/",className:"text-gray-800 hover:text-gray-600 transition-colors",children:"Home"}),(0,t.jsx)(n(),{href:"/articles/",className:"text-gray-800 transition-colors ",children:"Articles"}),(0,t.jsx)(n(),{href:"/topics/",className:"text-gray-800 hover:text-gray-600 transition-colors",children:"Topics"}),(0,t.jsx)(n(),{href:"/about/",className:"text-gray-800 hover:text-gray-600 transition-colors",children:"About"})]}),(0,t.jsx)(a.$,{variant:"outline",className:"border-blue-800 text-blue-800 hover:bg-purple-950 hover:text-white",children:"Github"})]})})}},6841:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(5150),i=r(5526);function n(...e){return(0,i.QP)((0,t.$)(e))}},7765:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(9864);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},8164:(e,s,r)=>{Promise.resolve().then(r.bind(r,2579))},8234:(e,s,r)=>{"use strict";r.d(s,{A:()=>h});var t=r(5921),i=r(1564),n=r.n(i),a=r(2069),l=r(7022),o=r(4435),c=r(1105),d=r(6910);function h(){return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"container mx-auto px-4",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(n(),{href:"/",className:"text-xl font-bold tracking-tighter",children:["Cline",(0,t.jsx)("span",{className:"text-blue-800",children:"Coder"})]}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Exploring the cutting edge of artificial intelligence and machine learning."}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)(n(),{href:"#",className:"text-gray-400 ",children:(0,t.jsx)(a.A,{className:"h-5 w-5"})}),(0,t.jsx)(n(),{href:"#",className:"text-gray-400 ",children:(0,t.jsx)(l.A,{className:"h-5 w-5"})}),(0,t.jsx)(n(),{href:"#",className:"text-gray-400 ",children:(0,t.jsx)(o.A,{className:"h-5 w-5"})}),(0,t.jsx)(n(),{href:"#",className:"text-gray-400 ",children:(0,t.jsx)(c.A,{className:"h-5 w-5"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium mb-4",children:"Topics"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-gray-400",children:[(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Artificial Intelligence"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Generative AI"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Computer Vision"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Deep Learning"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Machine Learning"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium mb-4",children:"Resources"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-gray-400",children:[(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Tutorials"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Research Papers"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Code Samples"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Datasets"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"#",className:"",children:"Tools"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium mb-4",children:"Contact"}),(0,t.jsx)("ul",{className:"space-y-2 text-sm text-gray-400",children:(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"<EMAIL>"})]})})]})]}),(0,t.jsx)("div",{className:"border-t border-gray-800 mt-12 pt-6 text-sm text-gray-400",children:(0,t.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," ClineCoder.com. All rights reserved.",(0,t.jsx)("span",{className:"guo-zi",children:"Guozi"})]})})]})})}},9110:()=>{},9273:(e,s,r)=>{"use strict";r.d(s,{Toaster:()=>a});var t=r(5921),i=r(4750),n=r(6737);let a=({...e})=>{let{theme:s="system"}=(0,i.D)();return(0,t.jsx)(n.l$,{theme:s,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}}};